import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from matplotlib.patches import Rectangle, Polygon
from matplotlib.patches import ConnectionPatch
from shapely.geometry import LineString
from matplotlib.backends.backend_pdf import PdfPages
import matplotlib.gridspec as gridspec

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class EvacuationPathAnalyzer:
    """疏散路径分析器"""


    def __init__(self, data_root):
        self.data_root = Path(data_root)
        self.output_dir = self.data_root / "analysis_results" / "behavioral" / "evacuation_path"
        self.output_dir.mkdir(parents=True, exist_ok=True)

        print(f"疏散路径分析文件路径为：{self.output_dir}")

        # 镜像参考点
        self.mirror_point = (-92.5, -0.4318, -0.4991)
        
        # 出口坐标
        self.left_exit = np.array([51.3, -2.424, -2.872])
        self.right_exit = np.array([-228.7, -2.424, -2.769])
        
        # NPC数量对应的颜色
        self.npc_colors = {0: 'gold', 10: 'darkorange', 20: 'darkred'}

        # 根据终点和折返情况定义的颜色规则
        self.trajectory_colors = {
            'left_exit': 'red', # 选择右出口
            'right_no_return': 'green', # 选择左出口，过程中没有折返
            'right_with_return': 'orange' # 选择左出口，过程中存在折返
        }

        # 定义决策区边界
        self.decision_zones = {
            'zone1':{
                'bound':[(-45.0, -40.0), (-1.345, 0.801)],
                'title':'决策区1'
            },
            'zone2':{
                'bound':[(-45.0, -40.0), (-3.238, -1.869)],
                'title':'决策区2'
            },
            'zone3':{
                'bound':[(-36.66, 6.51), (-3.328, -1.53)],
                'title':'决策区3'
            }
        }

        # 定义隧道结构坐标
        self.tunnel_structures = {
            'tunnel': [
                (60.000, 3.600),
                (60.000, -3.600),
                (-240.000, -3.600),
                (-240.000, 3.600)
            ],
            'tunnel_floor': [
                (52.0000, 2.0470),
                (52.0000, -1.4540),
                (-240.0000, -1.4540),
                (-240.0000, 2.0470)
            ],
            'train': [
                (22.0000, 1.4500),
                (22.0000, -1.4500),
                (-114.1000, -1.4500),
                (-114.1000, 1.4500)
            ],
            'emergency_platform': [
                (37.5200, -1.5500),
                (37.5200, -3.0200),
                (-217.0000, -3.0200),
                (-217.0000, -1.5500)
            ],
            'right_stairs': [
                (-217.0000, -1.5500),
                (-217.0200, -2.7600),
                (-218.4200, -2.7600),
                (-218.4200, -1.5500)
            ],
            'left_stairs': [
                (39.0000, -1.5500),
                (39.0000, -2.7600),
                (37.5200, -2.7600),
                (37.5200, -1.5500)
            ]
        }

    def draw_tunnel_structures(self, ax):
        """绘制隧道结构底图"""
        # 定义结构颜色和样式
        structure_styles = {
            'tunnel': {'facecolor': 'lightgray', 'edgecolor': 'black', 'alpha': 0.3, 'linewidth': 2, 'label': '隧道'},
            'tunnel_floor': {'facecolor': 'gray', 'edgecolor': 'black', 'alpha': 0.5, 'linewidth': 1.5, 'label': '隧道地面'},
            'train': {'facecolor': 'deepskyblue', 'edgecolor': 'darkblue', 'alpha': 0.3, 'linewidth': 2, 'label': '列车'},
            'emergency_platform': {'facecolor': 'silver', 'edgecolor': 'dimgray', 'alpha': 0.7, 'linewidth': 1.5, 'label': '应急平台'},
            'right_stairs': {'facecolor': 'silver', 'edgecolor': 'darkgreen', 'alpha': 0.8, 'linewidth': 1, 'label': '右楼梯'},
            'left_stairs': {'facecolor': 'silver', 'edgecolor': 'darkgreen', 'alpha': 0.8, 'linewidth': 1, 'label': '左楼梯'}
        }

        # 绘制每个结构
        for structure_name, coordinates in self.tunnel_structures.items():
            style = structure_styles[structure_name]

            # 创建多边形
            polygon = Polygon(coordinates, **style)
            ax.add_patch(polygon)

    def draw_tunnel_structures_in_zone(self, ax, x_bounds, z_bounds):
        """在局部放大图中绘制隧道结构底图（仅绘制在指定区域内的部分）"""
        # 定义结构颜色和样式（不显示标签，避免重复）
        structure_styles = {
            'tunnel': {'facecolor': 'lightgray', 'edgecolor': 'black', 'alpha': 0.3, 'linewidth': 1},
            'tunnel_floor': {'facecolor': 'gray', 'edgecolor': 'black', 'alpha': 0.5, 'linewidth': 1},
            'train': {'facecolor': 'lightskyblue', 'edgecolor': 'darkblue', 'alpha': 0.6, 'linewidth': 1},
            'emergency_platform': {'facecolor': 'darkgray', 'edgecolor': 'darkorange', 'alpha': 0.7, 'linewidth': 1},
            'right_stairs': {'facecolor': 'gray', 'edgecolor': 'darkgreen', 'alpha': 0.8, 'linewidth': 1},
            'left_stairs': {'facecolor': 'gray', 'edgecolor': 'darkgreen', 'alpha': 0.8, 'linewidth': 1}
        }

        # 绘制每个结构（仅当结构与区域有交集时）
        for structure_name, coordinates in self.tunnel_structures.items():
            # 检查结构是否与当前区域有交集
            if self.structure_intersects_zone(coordinates, x_bounds, z_bounds):
                style = structure_styles[structure_name]

                # 创建多边形
                polygon = Polygon(coordinates, **style)
                ax.add_patch(polygon)

    def structure_intersects_zone(self, coordinates, x_bounds, z_bounds):
        """检查结构是否与指定区域有交集"""
        # 获取结构的边界
        x_coords = [coord[0] for coord in coordinates]
        z_coords = [coord[1] for coord in coordinates]

        struct_x_min, struct_x_max = min(x_coords), max(x_coords)
        struct_z_min, struct_z_max = min(z_coords), max(z_coords)

        # 检查是否有交集
        return not (struct_x_max < x_bounds[0] or struct_x_min > x_bounds[1] or
                   struct_z_max < z_bounds[0] or struct_z_min > z_bounds[1])

    def classify_trajectory(self, trajectory):
        """根据终点位置和折返情况对轨迹进行分类"""

        x_coords = trajectory['x']
        z_coords = trajectory['z']

        # 判断终点位置
        final_pos = np.array ([x_coords[-1], 0, z_coords[-1]])
        distance_left = np.linalg.norm(final_pos - self.left_exit)
        distance_right = np.linalg.norm(final_pos - self.right_exit)

        is_left_exit = distance_left < distance_right

        if is_left_exit:
            return 'left_exit'
        else:
            # 检测是否存在折返
            has_return = self.detect_return_by_direction(x_coords, z_coords)
            if has_return:
                return 'right_with_return'
            else:
                return 'right_no_return'
            
    def detect_path_return(self, x_coords, z_coords):
        """检查路径是否存在折返"""
        if len(x_coords) < 4:
            print("路径点数量小于4,无法检测折返")
            return False
        
        try:
           # 使用X-Z平面创建路径线
            points = list(zip(x_coords, z_coords))
            line = LineString(points)
            # 检查自交
            return not line.is_simple
        except:
            # 如果shapely不可用，使用简单的方向变化检测
            return self.detect_return_by_direction(x_coords, z_coords)
        
    def detect_return_by_direction(self, x_coords, z_coords):
        """通过方向变化检测折返"""
        if len(x_coords) < 10:
            print("路径点数量小于10,无法检测折返")
            return False
        
        # 计算移动方向（主要看X方向的变化）
        x_diffs = np.diff(x_coords)
        
        # 统计向左和向右移动的距离
        left_movement = np.sum(x_diffs[x_diffs > 0])  # X增加为向左
        right_movement = np.sum(np.abs(x_diffs[x_diffs < 0]))  # X减少为向右
        
        # 如果两个方向的移动距离都超过一定阈值，认为有折返
        threshold = 5.0  # 5米
        return left_movement > threshold and right_movement > threshold

    def analyze_all_paths(self):
        """分析所有疏散路径数据"""
        print("###开始分析所有疏散路径数据###")
        
        # 读取总标签文件
        label_file = self.data_root / "all_file_labels.csv"
        if not label_file.exists():
            print("未找到总标签文件")
            return
        
        labels_df = pd.read_csv(label_file, encoding='utf-8-sig', engine='python')

        # 筛选第二部分实验的Subject文件
        subject_files = labels_df[(labels_df['data_type'] == 1) & 
                                 (labels_df['experiment'] == 2)].copy()
        
        if len(subject_files) == 0:
            print("未找到第二部分实验数据")
            return
        
        print(f"找到{len(subject_files)}个第二部分实验数据文件")

        # 按烟气浓度+NPC数量进行全量分组
        smoke_npc_combinations = [
            (1, 0), (1, 10), (1, 20),   # 低浓度
            (2, 0), (2, 10), (2, 20),   # 中浓度  
            (3, 0), (3, 10), (3, 20)    # 高浓度
        ]
        
        for smoke_level, npc_count in smoke_npc_combinations:
            # 筛选特定烟气浓度和NPC数量的数据
            condition_data = subject_files[
                (subject_files['smoke_level'] == smoke_level) & 
                (subject_files['npc_count'] == npc_count)
            ].copy()
            
            if len(condition_data) > 0:
                self.plot_smoke_npc_paths(condition_data, smoke_level, npc_count)
            else:
                print(f"烟气等级{smoke_level}+NPC{npc_count}的数据为空，跳过")
        
        print("###所有疏散路径分析完成###")

    def get_decision_zone4_bounds(self, smoke_level):
        """根据烟气等级获取决策区4的边界"""

        if smoke_level == 1:
            return [(17.99, 39.088), (-3, -1.3)]
        elif smoke_level == 2:
            return [(33.472, 39.088), (-3, -1.3)]
        else:
            return [(41.866, 47.62), (-1.3, 1.615)]
        
    def plot_smoke_npc_paths(self, condition_data, smoke_level, npc_count):
        """绘制特定烟气浓度+NPC数量组合的疏散路径图"""
        print(f"###开始绘制烟气等级{smoke_level}+NPC{npc_count}的疏散路径图###")


        # 创建图形布局
        fig = plt.figure(figsize=(20, 12))
        gs = gridspec.GridSpec(2, 4, height_ratios=[3,1], width_ratios=[1,1,1,1])

        # 主图
        ax_main = fig.add_subplot(gs[0, :])

        # 四个子图
        ax_zone1 = fig.add_subplot(gs[1, 0])
        ax_zone2 = fig.add_subplot(gs[1, 1])
        ax_zone3 = fig.add_subplot(gs[1, 2])
        ax_zone4 = fig.add_subplot(gs[1, 3])

        zone_axes = [ax_zone1, ax_zone2, ax_zone3, ax_zone4]

        # 获取决策区4的边界
        zone4_bounds = self.get_decision_zone4_bounds(smoke_level)
        decision_zones = self.decision_zones.copy()
        decision_zones['zone4'] = {
            'bound': zone4_bounds,
            'title': '决策区4'
        }

        # 根据NPC数量选择颜色
        color = self.npc_colors[npc_count]

        # 收集并分类所有轨迹数据
        classified_trajectories = {
            'left_exit':[],
            'right_no_return':[],
            'right_with_return':[]
        }
        
        # 收集所有轨迹数据
        all_trajectories = []
        for _, row in condition_data.iterrows():
            trajectory = self.load_and_process_trajectory(row)
            if trajectory is not None:
                #all_trajectories.append(trajectory)
                traj_type = self.classify_trajectory(trajectory)
                classified_trajectories[traj_type].append(trajectory)

        # 检查是否有有效轨迹
        total_trajectories = sum(len(trajs) for trajs in classified_trajectories.values())
        if total_trajectories == 0:
            print(f"烟气等级{smoke_level}+NPC{npc_count}没有有效轨迹数据")
            plt.close()
            return

        '''
        if len(all_trajectories) == 0:
            print(f"烟气等级{smoke_level}+NPC{npc_count}没有有效轨迹数据")
            plt.close()
            return
        '''

        # 绘制主图轨迹
        legend_added = set()
        for traj_type, trajectories in classified_trajectories.items():
            color = self.trajectory_colors[traj_type]

            for i, traj in enumerate(trajectories):
                # 每种类型添加一次图例
                if traj_type not in legend_added:
                    if traj_type == 'left_exit':
                        label = 'left_exit'
                    elif traj_type == 'right_no_return':
                        label = 'right_no_return'
                    else: 
                        label = 'right_with_return'
                    legend_added.add(traj_type)
                else:
                    label = ""

                ax_main.plot(traj['x'], traj['z'], color=color, alpha=0.8, 
                       linewidth=2, label=label)
        '''
        for i, traj in enumerate(all_trajectories):
            label = f'烟气{smoke_level}+NPC{npc_count}' if i == 0 else ""
            ax_main.plot(traj['x'], traj['z'], color=color, alpha=0.8, 
                   linewidth=1, label=label)
        '''

        # 绘制局部放大图轨迹
        for i, (zone_name, zone_info) in enumerate(decision_zones.items()):
            ax = zone_axes[i]
            x_bounds, z_bounds = zone_info['bound']
            
            for traj_type, trajectories in classified_trajectories.items():
                color = self.trajectory_colors[traj_type]

                for traj in trajectories:
                    # 获取连续的轨迹段
                    continuous_segments = self.get_continuous_segments_in_zone(traj, x_bounds, z_bounds)

                    # 分别绘制每个连续段
                    for segment in continuous_segments:
                        if len(segment['x']) > 1:
                            ax.plot(segment['x'], segment['z'], color=color, linewidth=2, alpha=1)
                    '''
                    # 筛选处在决策区内的轨迹点
                    mask = ((traj['x'] >= x_bounds[0]) & (traj['x'] <= x_bounds[1]) & 
                        (traj['z'] >= z_bounds[0]) & (traj['z'] <= z_bounds[1]))
                    
                    if mask.any():
                        ax.plot(traj['x'][mask], traj['z'][mask], color=color, linewidth=2, alpha=1)
                    '''

        # 设置主图
        self.setup_main_plot(ax_main, smoke_level, npc_count)

        # 设置局部放大图
        for i, (zone_name, zone_info) in enumerate(decision_zones.items()):
            self.setup_zone_plot(zone_axes[i], zone_info, smoke_level)

        # 添加连接线
        self.add_connection_lines(fig, ax_main, zone_axes, decision_zones, smoke_level)

        # 保存图片
        filename = f"烟气等级{smoke_level}_NPC{npc_count}_疏散路径图.svg"
        save_path = self.output_dir / filename
        plt.tight_layout()
        plt.savefig(save_path, dpi=600, bbox_inches='tight', format='svg')
        plt.close()

        print(f"烟气等级{smoke_level}+NPC{npc_count}的疏散路径图已保存: {filename}")
    
    def get_continuous_segments_in_zone(self, trajectory, x_bounds, z_bounds):
        """获取处在指定区域内的连续轨迹段"""
        x_coords = trajectory['x']
        z_coords = trajectory['z']

        # 判断每个点是否在决策区，这是一个布尔数组
        in_zone = ((x_coords >= x_bounds[0]) & (x_coords <= x_bounds[1]) & 
                   (z_coords >= z_bounds[0]) & (z_coords <= z_bounds[1]))
        
        segments = []
        current_segment_x = []
        current_segment_z = []

        for i in range(len(x_coords)):
            if in_zone[i]:
                current_segment_x.append(x_coords[i])
                current_segment_z.append(z_coords[i])
            else:
                if len(current_segment_x) > 0:
                    segments.append({
                        'x':np.array(current_segment_x),
                        'z':np.array(current_segment_z)
                    })
                    current_segment_x = []
                    current_segment_z = []

        if len(current_segment_x) > 0:
            segments.append({
                'x':np.array(current_segment_x),
                'z':np.array(current_segment_z)
            })
        
        return segments
 
    def add_connection_lines(self, fig, ax_main, zone_axes, decision_zones, smoke_level):
        """添加主图决策区与局部放大图之间的连接线"""
    
        for i, (zone_name, zone_info) in enumerate(decision_zones.items()):
            x_bounds, z_bounds = zone_info['bound']
            ax_zone = zone_axes[i]
        
            # 计算决策区中心点
            center_x = (x_bounds[0] + x_bounds[1]) / 2
            center_z = (z_bounds[0] + z_bounds[1]) / 2
        
            # 计算决策区的四个角点，选择最适合连接的角点
            corners = [
                (x_bounds[0], z_bounds[0]),  # 左下
                (x_bounds[1], z_bounds[0]),  # 右下
                (x_bounds[0], z_bounds[1]),  # 左上
                (x_bounds[1], z_bounds[1])   # 右上
            ]
        
            # 根据子图位置选择连接点
            if i == 0:  # zone1 - 左下子图
                main_point = corners[0]  # 左下角
                zone_point = (x_bounds[0], z_bounds[1])  # 子图左上角
            elif i == 1:  # zone2 - 左中子图
                main_point = corners[0]  # 左下角
                zone_point = (x_bounds[0], z_bounds[1])  # 子图左上角
            elif i == 2:  # zone3 - 右中子图
                main_point = corners[1]  # 右下角
                zone_point = (x_bounds[1], z_bounds[1])  # 子图右上角
            else:  # zone4 - 右下子图
                main_point = corners[1]  # 右下角
                zone_point = (x_bounds[1], z_bounds[1])  # 子图右上角
        
            # 创建连接线
            con1 = ConnectionPatch(
                xyA=main_point, xyB=zone_point,
                coordsA='data', coordsB='data',
                axesA=ax_main, axesB=ax_zone,
                color='darkblue', linewidth=1, alpha=0.5,
                linestyle='--'
            )
            fig.add_artist(con1)
            
            # 添加第二条连接线（从另一个角点）
            if i == 0:
                main_point2 = corners[1]  # 右下角
                zone_point2 = (x_bounds[1], z_bounds[1])  # 子图右上角
            elif i == 1:
                main_point2 = corners[1]  # 右下角
                zone_point2 = (x_bounds[1], z_bounds[1])  # 子图右上角
            elif i == 2:
                main_point2 = corners[0]  # 左下角
                zone_point2 = (x_bounds[0], z_bounds[1])  # 子图左上角
            else:
                main_point2 = corners[0]  # 左下角
                zone_point2 = (x_bounds[0], z_bounds[1])  # 子图左上角
            
            con2 = ConnectionPatch(
                xyA=main_point2, xyB=zone_point2,
                coordsA='data', coordsB='data',
                axesA=ax_main, axesB=ax_zone,
                color='darkblue', linewidth=1, alpha=0.5,
                linestyle='--'
            )
            fig.add_artist(con2)

    def load_and_process_trajectory(self, file_info):
        """加载并处理轨迹数据"""
        file_path = Path(file_info['file_path'])
        organized_file_path = file_path.parent / f"organized_clean_merged_{file_path.stem}.csv"

        if not organized_file_path.exists():
            print(f"文件不存在: {organized_file_path}")
            return None
        
        try:
            df = pd.read_csv(organized_file_path, encoding='utf-8-sig', engine='python')

            # 检查必要列
            required_cols = ['PosX', 'PosZ']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                print(f"缺少必要列: {missing_cols}")
                return None
            
            pos_x = df['PosX'].values
            pos_z = df['PosZ'].values

            # 处理镜像条件
            if file_info['is_mirror']:
                pos_x = 2 * self.mirror_point[0] - pos_x

            return {
                'x': pos_x,
                'z': pos_z
            }
        except Exception as e:
            print(f"处理轨迹数据时出错: {e}")
            return None
        
    def setup_main_plot(self, ax, smoke_level, npc_count):
        """设置主图"""
        # 首先绘制隧道结构底图
        self.draw_tunnel_structures(ax)

        # 绘制出口为长方形
        left_exit_rect = Rectangle(
            (self.left_exit[0] - 4, self.left_exit[2] - 0.5),
            8, 1,
            facecolor='firebrick', edgecolor='black',
            linewidth=2, alpha=0.8, label='左出口'
        )
        ax.add_patch(left_exit_rect)

        right_exit_rect = Rectangle(
            (self.right_exit[0] - 4, self.right_exit[2] - 0.5),
            8, 1,
            facecolor='yellowgreen', edgecolor='black',
            linewidth=2, alpha=0.8, label='右出口'
        )
        ax.add_patch(right_exit_rect)

        # 绘制决策区边界
        self.draw_decision_zones(ax, smoke_level)

        ax.set_xlim(-240, 60)
        ax.set_ylim(-10, 10)
        ax.set_xlabel('X坐标 (m)', fontsize=12)
        ax.set_ylabel('Z坐标 (m)', fontsize=12)
        ax.set_title(f'烟气等级{smoke_level} + NPC数量{npc_count} 疏散路径图', fontsize=14)
        ax.grid(True, alpha=0.3)
        ax.legend(loc='upper right')

    def setup_zone_plot(self, ax, zone_info, smoke_level):
        """设置局部放大图"""
        x_bounds, z_bounds = zone_info['bound']

        # 首先绘制隧道结构底图（在局部区域内的部分）
        self.draw_tunnel_structures_in_zone(ax, x_bounds, z_bounds)

        ax.set_xlim(x_bounds[0], x_bounds[1])
        ax.set_ylim(z_bounds[0], z_bounds[1])
        ax.set_xlabel('X_Axis(m)', fontsize=12)
        ax.set_ylabel('Z_Axis(m)', fontsize=12)
        ax.set_title(zone_info['title'], fontsize=10)
        ax.grid(True, alpha=0.3)

        # 绘制决策区边界
        rect = Rectangle((x_bounds[0], z_bounds[0]), x_bounds[1] - x_bounds[0], z_bounds[1] - z_bounds[0],
                        linewidth=1, edgecolor='black', facecolor='none')
        ax.add_patch(rect)

    def draw_decision_zones(self, ax, smoke_level):
        """在主图上绘制决策区域边界"""
        # 绘制决策区1-3
        for zone_name, zone_info in self.decision_zones.items():
            x_bounds, z_bounds = zone_info['bound']
            rect = Rectangle((x_bounds[0], z_bounds[0]), x_bounds[1] - x_bounds[0], z_bounds[1] - z_bounds[0],
                             linewidth=2, edgecolor='darkblue', facecolor='none', linestyle='--', alpha=0.7)
            ax.add_patch(rect)
        # 绘制决策区4
        zone4_bounds = self.get_decision_zone4_bounds(smoke_level)
        x_bounds, z_bounds = zone4_bounds
        rect = Rectangle((x_bounds[0], z_bounds[0]), x_bounds[1] - x_bounds[0], z_bounds[1] - z_bounds[0],
                         linewidth=2, edgecolor='darkblue', facecolor='none', linestyle='--', alpha=0.7)
        ax.add_patch(rect)

def get_grand_parent_folder():
    current_script_path = os.path.abspath(__file__)
    current_folder = os.path.dirname(current_script_path)
    parent_folder = os.path.dirname(current_folder)
    return parent_folder


def get_target_file_path():
    return get_grand_parent_folder() + os.sep + "ScriptsTest"

if __name__ == "__main__":
    # 创建分析器实例
    analyzer = EvacuationPathAnalyzer(get_target_file_path())

    # 分析所有数据
    analyzer.analyze_all_paths()
