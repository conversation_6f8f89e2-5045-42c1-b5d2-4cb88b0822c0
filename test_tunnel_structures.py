import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Polygon
import sys
import os

# 添加Scripts目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'Scripts'))

from EvacuationPathAnalysis import EvacuationPathAnalyzer

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def test_tunnel_structures():
    """测试隧道结构绘制功能"""
    
    # 创建分析器实例
    analyzer = EvacuationPathAnalyzer("ScriptsTest")
    
    # 创建测试图形
    fig, ax = plt.subplots(1, 1, figsize=(16, 8))
    
    # 绘制隧道结构
    analyzer.draw_tunnel_structures(ax)
    
    # 绘制出口
    left_exit_rect = plt.Rectangle(
        (analyzer.left_exit[0] - 1, analyzer.left_exit[2] - 0.5),
        2, 1,
        facecolor='firebrick', edgecolor='black', 
        linewidth=2, alpha=0.8, label='左出口'
    )
    ax.add_patch(left_exit_rect)
    
    right_exit_rect = plt.Rectangle(
        (analyzer.right_exit[0] - 1, analyzer.right_exit[2] - 0.5),
        2, 1,
        facecolor='yellowgreen', edgecolor='black', 
        linewidth=2, alpha=0.8, label='右出口'
    )
    ax.add_patch(right_exit_rect)
    
    # 设置图形属性
    ax.set_xlim(-250, 60)
    ax.set_ylim(-10, 10)
    ax.set_xlabel('X坐标 (m)', fontsize=12)
    ax.set_ylabel('Z坐标 (m)', fontsize=12)
    ax.set_title('隧道结构底图测试', fontsize=14)
    ax.grid(True, alpha=0.3)
    ax.legend(loc='upper right')
    
    # 保存图片
    plt.tight_layout()
    plt.savefig('tunnel_structures_test.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("隧道结构底图测试完成，图片已保存为 tunnel_structures_test.png")

if __name__ == "__main__":
    test_tunnel_structures()
