import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Polygon, Rectangle

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def test_tunnel_structures():
    """测试隧道结构绘制功能"""

    # 定义隧道结构坐标
    tunnel_structures = {
        'tunnel': [
            (60.000, 3.600),
            (60.000, -3.600),
            (-240.000, -3.600),
            (-240.000, 3.600)
        ],
        'tunnel_floor': [
            (52.0000, 2.0470),
            (52.0000, -1.4540),
            (-240.0000, -1.4540),
            (-240.0000, 2.0470)
        ],
        'train': [
            (22.0000, 1.4500),
            (22.0000, -1.4500),
            (-114.1000, -1.4500),
            (-114.1000, 1.4500)
        ],
        'emergency_platform': [
            (37.5200, -1.8600),
            (37.5200, -3.0200),
            (-217.0000, -3.0200),
            (-217.0000, -1.8600)
        ],
        'right_stairs': [
            (-217.0000, -1.8600),
            (-217.0200, -2.7600),
            (-218.4200, -2.7600),
            (-218.4200, -1.8600)
        ],
        'left_stairs': [
            (39.0000, -1.8600),
            (39.0000, -2.7600),
            (37.5200, -2.7600),
            (37.5200, -1.8600)
        ]
    }

    # 定义结构颜色和样式
    structure_styles = {
        'tunnel': {'facecolor': 'lightgray', 'edgecolor': 'black', 'alpha': 0.3, 'linewidth': 2, 'label': '隧道'},
        'tunnel_floor': {'facecolor': 'darkgray', 'edgecolor': 'black', 'alpha': 0.5, 'linewidth': 1.5, 'label': '隧道地面'},
        'train': {'facecolor': 'blue', 'edgecolor': 'darkblue', 'alpha': 0.6, 'linewidth': 2, 'label': '列车'},
        'emergency_platform': {'facecolor': 'orange', 'edgecolor': 'darkorange', 'alpha': 0.7, 'linewidth': 1.5, 'label': '应急平台'},
        'right_stairs': {'facecolor': 'green', 'edgecolor': 'darkgreen', 'alpha': 0.8, 'linewidth': 1, 'label': '右楼梯'},
        'left_stairs': {'facecolor': 'green', 'edgecolor': 'darkgreen', 'alpha': 0.8, 'linewidth': 1, 'label': '左楼梯'}
    }

    # 出口坐标
    left_exit = np.array([51.3, -2.424, -2.872])
    right_exit = np.array([-228.7, -2.424, -2.769])

    # 创建测试图形
    fig, ax = plt.subplots(1, 1, figsize=(16, 8))

    # 绘制隧道结构
    for structure_name, coordinates in tunnel_structures.items():
        style = structure_styles[structure_name]
        polygon = Polygon(coordinates, **style)
        ax.add_patch(polygon)

    # 绘制出口
    left_exit_rect = Rectangle(
        (left_exit[0] - 1, left_exit[2] - 0.5),
        2, 1,
        facecolor='firebrick', edgecolor='black',
        linewidth=2, alpha=0.8, label='左出口'
    )
    ax.add_patch(left_exit_rect)

    right_exit_rect = Rectangle(
        (right_exit[0] - 1, right_exit[2] - 0.5),
        2, 1,
        facecolor='yellowgreen', edgecolor='black',
        linewidth=2, alpha=0.8, label='右出口'
    )
    ax.add_patch(right_exit_rect)

    # 设置图形属性
    ax.set_xlim(-250, 60)
    ax.set_ylim(-10, 10)
    ax.set_xlabel('X坐标 (m)', fontsize=12)
    ax.set_ylabel('Z坐标 (m)', fontsize=12)
    ax.set_title('隧道结构底图测试', fontsize=14)
    ax.grid(True, alpha=0.3)
    ax.legend(loc='upper right')

    # 保存图片
    plt.tight_layout()
    plt.savefig('tunnel_structures_test.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("隧道结构底图测试完成，图片已保存为 tunnel_structures_test.png")

if __name__ == "__main__":
    test_tunnel_structures()
